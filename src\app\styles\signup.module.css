.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: url('/assets/background_landing_signin.jpg') no-repeat center center fixed;
  background-size: 100% 100%;
  overflow-x: hidden;
}

.page {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 100px;
  margin-bottom: 20px;
}

.signupForm {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 17px;
  border-radius: 10px;
  width: 90%;
  max-width: 320px;
  color: #fff;
  margin-top: -50px;
}

.title {
  font-family: Quicksand;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 10px;
}

.errorMessage {
  background-color: rgba(255, 0, 0, 0.2);
  color: #ff6b6b;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
  border: 1px solid rgba(255, 0, 0, 0.3);
  font-size: 14px;
}

.successMessage {
  background-color: rgba(0, 255, 0, 0.2);
  color: #47CF73;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
  border: 1px solid rgba(0, 255, 0, 0.3);
  font-size: 14px;
}

.inputField {
  width: 90%;
  padding: 8px;
  margin-bottom: 15px;
  border-radius: 5px;
  border: none;
  outline: none;
  background-color: white;
  color: black;
}

.passwordInput {
  position: relative;
  margin-bottom: -5px;
  padding: 10px;
  width: 81%;
  margin-left: -10px;
}

.passwordInput input {
  width: 100%;
  padding: 10px;
  padding-right: 40px;
  border-radius: 5px;
  border: none;
  background-color: white;
  color: black;
  outline: none;
}

.togglePassword {
  position: absolute;
    top: 30%;
    right: -10px;
    cursor: pointer;
}

.submitButton {
  margin-top: 10px;
  padding: 10px;
  width: 60%;
  background-color: #e98e0f;
  color: #fff;
  font-family: Quicksand;
  font-weight: 700;
  margin-left: 70px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.submitButton:disabled {
  background-color: #999;
  cursor: not-allowed;
}

.loginRedirect {
  font-size: 12px;
  text-align: center;
  margin-top: 10px;
}

.loginRedirect a {
  color: #e98e0f;
  text-decoration: none;
}
